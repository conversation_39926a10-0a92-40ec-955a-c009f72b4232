# Generated by Cargo
**/target/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
# Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

# VS Code settings
.vscode/

# IntelliJ/RustRover
.idea/
*.iml

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Swap files
*.swp
*.swo

# Temporary files
*.tmp
*.log

# Local development files
.env
.env.*
!.env.example

# Demo scripts (not needed in version control)
demo.sh
demo_with_spinners.sh

# Test secrets
test_secret.txt
sample_passwords.csv

# Vault files
vault.encrypted
vault.encrypted.files/

# Local overrides
Cargo.local.toml