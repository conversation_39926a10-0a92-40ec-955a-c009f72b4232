[package]
name = "ciphervault"
version = "0.1.0"
edition = "2021"

[dependencies]
# Cryptography
aes-gcm = "0.10.3"
argon2 = "0.5.2"
rand = "0.8.5"
base64 = "0.21.5"
hex = "0.4.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
csv = "1.3"

# File handling
tokio = { version = "1.0", features = ["full"] }
walkdir = "2.4"

# CLI
clap = { version = "4.4", features = ["derive"] }
dialoguer = "0.11"
console = "0.15"
indicatif = "0.17"

# Utilities
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.6", features = ["v4", "serde"] }
anyhow = "1.0"
thiserror = "1.0"
zeroize = "1.7"
throbber = "0.1.4"
arboard = "3.6.0"
