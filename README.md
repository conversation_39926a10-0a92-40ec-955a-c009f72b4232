# CipherVault – The Most Secure Password Vault

```text
 ██████╗██╗██████╗ ██╗  ██╗███████╗██████╗ ██╗   ██╗ █████╗ ██╗   ██╗██╗  ████████╗
██╔════╝██║██╔══██╗██║  ██║██╔════╝██╔══██╗██║   ██║██╔══██╗██║   ██║██║  ╚══██╔══╝
██║     ██║██████╔╝███████║█████╗  ██████╔╝██║   ██║███████║██║   ██║██║     ██║
██║     ██║██╔═══╝ ██╔══██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══██║██║   ██║██║     ██║
╚██████╗██║██║     ██║  ██║███████╗██║  ██║ ╚████╔╝ ██║  ██║╚██████╔╝███████╗██║
 ╚═════╝╚═╝╚═╝     ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝
```

A secure command-line password vault built in Rust with military-grade encryption.

## Features

- **AES-256-GCM encryption** with Argon2 key derivation
- **Categories and favorites** for organization
- **Secure clipboard** with auto-clear timeout
- **File storage** for any file type
- **CSV import/export** for data migration
- **Throbber animations** for professional CLI experience
- **Cross-platform** support

## Installation

### Prerequisites

- Rust 1.70+ and Cargo
- Git

### From Source

```bash
git clone https://github.com/valonmulolli/ciphervault.git
cd ciphervault
cargo build --release
```

The binary will be available at `target/release/ciphervault`

### Install Globally

```bash
cargo install --path .
```

This installs `ciphervault` to your Cargo bin directory (usually `~/.cargo/bin/`)

### Add to PATH

Add the binary location to your PATH:

```bash
# For local build
export PATH="$PATH:$(pwd)/target/release"

# For global install (if ~/.cargo/bin not in PATH)
export PATH="$PATH:~/.cargo/bin"
```

### Verify Installation

```bash
ciphervault --version
```

## Usage

```bash
# Basic commands
ciphervault init                              # Initialize vault
ciphervault add-password --title "GitHub" --username "user" --category "Work"
ciphervault list                              # List all entries
ciphervault copy "GitHub"                     # Copy to clipboard

# Organization
ciphervault list --category "Work"            # Filter by category
ciphervault list --favorites                  # Show favorites only
ciphervault search "github"                   # Search entries

# Files and data
ciphervault add-file --file-path secret.pdf
ciphervault export --output backup.csv
ciphervault import --input passwords.csv
```

## Security

- **AES-256-GCM** encryption with authenticated encryption
- **Argon2** memory-hard key derivation
- **Auto-clearing clipboard** (30-second timeout)
- **Secure memory wiping** of sensitive data
- **No plaintext storage** - everything encrypted at rest

## Development

Requirements: Rust 1.70+

```bash
cargo build --release
cargo test
```

## License

MIT License - see [LICENSE](LICENSE) file for details.
