use console::style;
use indicatif::{ProgressBar, ProgressStyle};
use std::time::Duration;

#[allow(dead_code)]

/// Creates a new throbber with custom style and message
pub fn create_throbber(message: &str) -> ProgressBar {
    let pb = ProgressBar::new_spinner();
    pb.set_style(
        ProgressStyle::default_spinner()
            .tick_chars("⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏")
            .template("{spinner:.cyan} {msg}")
            .unwrap(),
    );
    pb.set_message(message.to_string());
    pb.enable_steady_tick(Duration::from_millis(80));
    pb
}

/// Shows a throbber with a message and returns a function to stop it
#[allow(dead_code)]
pub fn with_throbber<F, T>(message: &str, operation: F) -> T
where
    F: FnOnce() -> T,
{
    let throbber = create_throbber(message);
    let result = operation();
    throbber.finish_and_clear();
    result
}

/// Shows a throbber while performing an async operation
#[allow(dead_code)]
pub async fn with_throbber_async<F, Fut, T>(message: &str, operation: F) -> T
where
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = T>,
{
    let throbber = create_throbber(message);
    let result = operation().await;
    throbber.finish_and_clear();
    result
}

/// Shows a throbber with a success message when done
#[allow(dead_code)]
pub fn with_throbber_success<F, T>(message: &str, success_message: &str, operation: F) -> T
where
    F: FnOnce() -> T,
{
    let throbber = create_throbber(message);
    let result = operation();
    throbber.finish_with_message(style(success_message).green().to_string());
    result
}

/// Shows a throbber with an error message if the operation fails
#[allow(dead_code)]
pub fn with_throbber_result<F, T, E>(
    message: &str,
    success_message: &str,
    operation: F,
) -> Result<T, E>
where
    F: FnOnce() -> Result<T, E>,
{
    let throbber = create_throbber(message);
    let result = operation();
    match result {
        Ok(_) => throbber.finish_with_message(style(success_message).green().to_string()),
        Err(_) => throbber.finish_with_message(style("Failed!").red().to_string()),
    }
    result
}

/// Shows a throbber with a progress message that updates
pub struct ProgressThrobber {
    pb: ProgressBar,
    counter: usize,
}

impl ProgressThrobber {
    pub fn new(message: &str) -> Self {
        let pb = create_throbber(message);
        Self { pb, counter: 0 }
    }

    pub fn tick(&mut self) {
        self.counter = (self.counter + 1) % 4;
        let dots = ".".repeat(self.counter + 1);
        self.pb
            .set_message(format!("{} {}", self.pb.message(), dots));
        self.pb.tick();
    }

    pub fn finish_with_message(&self, message: &str) {
        self.pb
            .finish_with_message(style(message).green().to_string());
    }

    #[allow(dead_code)]
    pub fn finish_with_error(&self, message: &str) {
        self.pb
            .finish_with_message(style(message).red().to_string());
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;

    #[test]
    fn test_throbber_creation() {
        let throbber = create_throbber("Testing...");
        assert!(!throbber.is_finished());
        throbber.finish_and_clear();
    }

    #[test]
    fn test_with_throbber() {
        let result = with_throbber("Testing...", || {
            thread::sleep(Duration::from_millis(100));
            42
        });
        assert_eq!(result, 42);
    }
}
