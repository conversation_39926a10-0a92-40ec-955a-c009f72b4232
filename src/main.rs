mod cli;
mod clipboard;
mod crypto;
mod models;
mod storage;
mod utils;

use anyhow::Result;
use clap::{CommandFactory, Parser, Subcommand};
use console::style;

#[derive(Parser)]
#[command(name = "ciphervault")]
#[command(about = "The most secure password vault ever")]
#[command(version)]
#[command(disable_help_subcommand = true)]
#[command(disable_help_flag = true)]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,

    #[arg(short, long, action = clap::ArgAction::Help, help = "Print help information")]
    help: Option<bool>,
}

#[derive(Subcommand)]
enum Commands {
    /// Initialize a new vault
    Init {
        /// Path to the vault file
        #[arg(short, long, default_value = "vault.encrypted")]
        vault_path: String,
        /// Master password
        #[arg(long)]
        master_password: Option<String>,
        /// Force operation without confirmation
        #[arg(long)]
        force: bool,
    },
    /// Add a password entry
    AddPassword {
        /// Title for the password entry
        #[arg(short, long)]
        title: String,
        /// Username
        #[arg(short, long)]
        username: String,
        /// Password (will prompt if not provided)
        #[arg(short, long)]
        password: Option<String>,
        /// URL
        #[arg(long)]
        url: Option<String>,
        /// Notes
        #[arg(short, long)]
        notes: Option<String>,
        /// Master password
        #[arg(long)]
        master_password: Option<String>,
        /// Path to the vault file
        #[arg(long, default_value = "vault.encrypted")]
        vault_path: String,
        /// Tags for the entry
        #[arg(long, value_delimiter = ',')]
        tags: Option<Vec<String>>,
        /// Category for the entry
        #[arg(short, long, default_value = "General")]
        category: String,
        /// Mark as favorite
        #[arg(long)]
        favorite: bool,
        /// Copy password to clipboard after creation
        #[arg(long)]
        copy: bool,
    },
    /// Add a file to the vault
    AddFile {
        /// Path to the file to add
        #[arg(short, long)]
        file_path: String,
        /// Description for the file
        #[arg(short, long)]
        description: Option<String>,
    },
    /// List all entries
    List {
        /// Master password
        #[arg(long)]
        master_password: Option<String>,
        /// Path to the vault file
        #[arg(long, default_value = "vault.encrypted")]
        vault_path: String,
        /// Filter by category
        #[arg(short, long)]
        category: Option<String>,
        /// Show only favorites
        #[arg(long)]
        favorites: bool,
        /// Show category statistics
        #[arg(long)]
        stats: bool,
    },
    /// Search for entries
    Search {
        /// Search query
        query: String,
    },
    /// Export entries to CSV
    Export {
        /// Output file path
        #[arg(short, long)]
        output: String,
    },
    /// Import entries from CSV
    Import {
        /// Input CSV file path
        #[arg(short, long)]
        input: String,
    },
    /// Change master password
    ChangePassword,
    /// Extract a file from the vault
    ExtractFile {
        /// Filename in the vault (as listed in `list`)
        #[arg(short, long)]
        filename: String,
        /// Output path to save the decrypted file
        #[arg(short, long)]
        output: String,
    },
    /// Manage categories
    Category {
        #[command(subcommand)]
        action: CategoryAction,
    },
    /// Copy password to clipboard
    Copy {
        /// Entry title or ID to copy
        entry: String,
        /// Master password
        #[arg(long)]
        master_password: Option<String>,
        /// Path to the vault file
        #[arg(long, default_value = "vault.encrypted")]
        vault_path: String,
        /// Timeout in seconds (default: 30)
        #[arg(long, default_value = "30")]
        timeout: u64,
    },
    /// Show help message
    Help,
}

#[derive(Subcommand)]
enum CategoryAction {
    /// List all categories
    List,
    /// Add a new category
    Add {
        /// Category name
        name: String,
        /// Category description
        #[arg(short, long)]
        description: Option<String>,
        /// Category icon
        #[arg(short, long)]
        icon: Option<String>,
    },
    /// Show category statistics
    Stats,
}

#[tokio::main]
async fn main() -> Result<()> {
    println!(
        "{}",
        style("🔐 CipherVault - The Most Secure Password Vault")
            .bold()
            .cyan()
    );
    println!(
        "{}",
        style("===============================================").dim()
    );

    let cmd = Cli::command();
    let cli = match Cli::try_parse() {
        Ok(cli) => cli,
        Err(e) => {
            if e.kind() == clap::error::ErrorKind::DisplayHelp {
                cli::show_help_anim(cmd).await;
                return Ok(());
            }
            e.exit()
        }
    };

    let command = cli.command.unwrap_or(Commands::Help);

    if cli.help.is_some() {
        cli::show_help_anim(cmd).await;
        return Ok(());
    }

    match command {
        Commands::Init {
            vault_path,
            master_password,
            force,
        } => {
            cli::init_vault(&vault_path, master_password, force).await?;
        }
        Commands::AddPassword {
            title,
            username,
            password,
            url,
            notes,
            master_password,
            vault_path,
            tags,
            category,
            favorite,
            copy,
        } => {
            cli::add_password(
                title,
                username,
                password,
                url,
                notes,
                master_password,
                &vault_path,
                tags,
                category,
                favorite,
                copy,
            )
            .await?;
        }
        Commands::AddFile {
            file_path,
            description,
        } => {
            cli::add_file(file_path, description).await?;
        }
        Commands::List {
            master_password,
            vault_path,
            category,
            favorites,
            stats,
        } => {
            cli::list_entries(master_password, &vault_path, category, favorites, stats).await?;
        }
        Commands::Search { query } => {
            cli::search_entries(&query).await?;
        }
        Commands::Export { output } => {
            cli::export_entries(&output).await?;
        }
        Commands::Import { input } => {
            cli::import_entries(&input).await?;
        }
        Commands::ChangePassword => {
            cli::change_master_password().await?;
        }
        Commands::ExtractFile { filename, output } => {
            cli::extract_file(&filename, &output).await?;
        }
        Commands::Category { action } => {
            match action {
                CategoryAction::List => {
                    cli::list_categories().await?;
                }
                CategoryAction::Add { name, description, icon } => {
                    cli::add_category(name, description, icon).await?;
                }
                CategoryAction::Stats => {
                    cli::show_category_stats().await?;
                }
            }
        }
        Commands::Copy { entry, master_password, vault_path, timeout } => {
            cli::copy_password_to_clipboard(&entry, master_password, &vault_path, timeout).await?;
        }
        Commands::Help => {
            cli::show_help_anim(cmd).await;
        }
    }

    Ok(())
}
