use anyhow::{anyhow, Result};
use serde_json;
use std::fs;
use std::path::{Path, PathBuf};
use tokio::fs as tokio_fs;
use walkdir::WalkDir;

use crate::crypto::{decode_base64, encode_base64, CryptoManager};
use crate::models::{FileEntry, Vault};
use crate::utils;

pub struct VaultStorage {
    vault_path: PathBuf,
    files_dir: PathBuf,
    crypto_manager: Option<CryptoManager>,
    salt: Option<Vec<u8>>,
}

#[derive(serde::Serialize, serde::Deserialize)]
struct EncryptedVault {
    salt: String,
    data: String,
}

impl VaultStorage {
    pub fn new(vault_path: &str) -> Self {
        let vault_path = PathBuf::from(vault_path);
        let files_dir = vault_path.with_extension("files");

        Self {
            vault_path,
            files_dir,
            crypto_manager: None,
            salt: None,
        }
    }

    pub fn initialize(&mut self, master_password: &str) -> Result<()> {
        // Create directories
        if let Some(parent) = self.vault_path.parent() {
            fs::create_dir_all(parent)?;
        }
        fs::create_dir_all(&self.files_dir)?;

        // Generate salt and create crypto manager
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(master_password, &salt)?;

        self.salt = Some(salt.clone());
        self.crypto_manager = Some(crypto_manager);

        // Create initial vault
        let vault = Vault::new();
        self.save_vault(&vault)?;

        Ok(())
    }

    pub fn load(&mut self, master_password: &str) -> Result<Vault> {
        if !self.vault_path.exists() {
            return Err(anyhow!(
                "Vault file does not exist. Use 'init' command first."
            ));
        }

        // Read encrypted vault data
        let encrypted_data = fs::read_to_string(&self.vault_path)?;
        let encrypted_vault: EncryptedVault = serde_json::from_str(&encrypted_data)?;

        // Decode salt and data
        let salt = decode_base64(&encrypted_vault.salt)?;
        let encrypted_bytes = decode_base64(&encrypted_vault.data)?;

        // Create crypto manager and decrypt
        let crypto_manager = CryptoManager::new(master_password, &salt)?;
        let decrypted_data = crypto_manager.decrypt(&encrypted_bytes)?;

        // Deserialize vault
        let vault: Vault = serde_json::from_slice(&decrypted_data)?;

        self.salt = Some(salt);
        self.crypto_manager = Some(crypto_manager);

        Ok(vault)
    }

    pub fn save_vault(&self, vault: &Vault) -> Result<()> {
        let crypto_manager = self
            .crypto_manager
            .as_ref()
            .ok_or_else(|| anyhow!("Crypto manager not initialized"))?;

        let salt = self
            .salt
            .as_ref()
            .ok_or_else(|| anyhow!("Salt not initialized"))?;

        // Serialize vault
        let vault_data = serde_json::to_vec(vault)?;

        // Encrypt vault data
        let encrypted_data = crypto_manager.encrypt(&vault_data)?;

        // Create encrypted vault structure
        let encrypted_vault = EncryptedVault {
            salt: encode_base64(salt),
            data: encode_base64(&encrypted_data),
        };

        // Save to file
        let json_data = serde_json::to_string_pretty(&encrypted_vault)?;
        fs::write(&self.vault_path, json_data)?;

        Ok(())
    }

    pub async fn add_file(
        &self,
        file_path: &str,
        description: Option<String>,
    ) -> Result<FileEntry> {
        let file_path = Path::new(file_path);
        if !file_path.exists() {
            return Err(anyhow!("File does not exist: {}", file_path.display()));
        }

        let crypto_manager = self
            .crypto_manager
            .as_ref()
            .ok_or_else(|| anyhow!("Crypto manager not initialized"))?;

        // Read file content
        let file_content = tokio_fs::read(file_path).await?;
        let file_size = file_content.len() as u64;

        // Determine MIME type
        let mime_type = utils::guess_mime_type(file_path);

        // Generate unique filename
        let filename = format!(
            "{}_{}",
            uuid::Uuid::new_v4(),
            file_path.file_name().unwrap().to_string_lossy()
        );

        // Encrypt file content
        let encrypted_content = crypto_manager.encrypt(&file_content)?;

        // Save encrypted file
        let encrypted_file_path = self.files_dir.join(&filename);
        tokio_fs::write(&encrypted_file_path, encrypted_content).await?;

        // Create file entry
        let entry = FileEntry {
            id: uuid::Uuid::new_v4(),
            filename,
            original_path: file_path.to_string_lossy().to_string(),
            description,
            file_size,
            mime_type,
            created_at: chrono::Utc::now(),
            tags: Vec::new(),
            category: "General".to_string(),
            favorite: false,
        };

        Ok(entry)
    }

    pub async fn extract_file(&self, file_entry: &FileEntry, output_path: &str) -> Result<()> {
        let crypto_manager = self
            .crypto_manager
            .as_ref()
            .ok_or_else(|| anyhow!("Crypto manager not initialized"))?;

        let encrypted_file_path = self.files_dir.join(&file_entry.filename);
        if !encrypted_file_path.exists() {
            return Err(anyhow!(
                "Encrypted file not found: {}",
                encrypted_file_path.display()
            ));
        }

        // Read encrypted file
        let encrypted_content = tokio_fs::read(&encrypted_file_path).await?;

        // Decrypt file content
        let decrypted_content = crypto_manager.decrypt(&encrypted_content)?;

        // Write to output path
        tokio_fs::write(output_path, decrypted_content).await?;

        Ok(())
    }

    #[allow(dead_code)]
    pub fn list_files(&self) -> Result<Vec<FileEntry>> {
        let mut files = Vec::new();

        for entry in WalkDir::new(&self.files_dir)
            .into_iter()
            .filter_map(|e| e.ok())
            .filter(|e| e.file_type().is_file())
        {
            let filename = entry.file_name().to_string_lossy().to_string();
            // Here you would typically load the file metadata from the vault
            // For now, we'll create a basic entry
            let file_entry = FileEntry {
                id: uuid::Uuid::new_v4(),
                filename,
                original_path: String::new(),
                description: None,
                file_size: entry.metadata()?.len(),
                mime_type: String::new(),
                created_at: chrono::Utc::now(),
                tags: Vec::new(),
                category: "General".to_string(),
                favorite: false,
            };
            files.push(file_entry);
        }

        Ok(files)
    }

    pub fn change_master_password(&mut self, new_password: &str) -> Result<()> {
        // Load current vault
        let vault_data = fs::read_to_string(&self.vault_path)?;
        let encrypted_vault: EncryptedVault = serde_json::from_str(&vault_data)?;

        // Decrypt with old password
        let salt = decode_base64(&encrypted_vault.salt)?;
        let encrypted_bytes = decode_base64(&encrypted_vault.data)?;
        let old_crypto_manager = CryptoManager::new("", &salt)?; // This would need the actual old password
        let decrypted_data = old_crypto_manager.decrypt(&encrypted_bytes)?;

        // Create new crypto manager with new password
        let new_salt = CryptoManager::generate_salt();
        let new_crypto_manager = CryptoManager::new(new_password, &new_salt)?;

        // Re-encrypt with new password
        let new_encrypted_data = new_crypto_manager.encrypt(&decrypted_data)?;

        // Save with new salt and encrypted data
        let new_encrypted_vault = EncryptedVault {
            salt: encode_base64(&new_salt),
            data: encode_base64(&new_encrypted_data),
        };

        let json_data = serde_json::to_string_pretty(&new_encrypted_vault)?;
        fs::write(&self.vault_path, json_data)?;

        // Update current instance
        self.salt = Some(new_salt);
        self.crypto_manager = Some(new_crypto_manager);

        Ok(())
    }
}
