use aes_gcm::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OsRng},
    Aes256Gcm, <PERSON>, Nonce,
};
use anyhow::{anyhow, Result};
use argon2::{
    password_hash::{
        rand_core::OsRng as ArgonOsRng, PasswordHash, PasswordHasher, PasswordVerifier, SaltString,
    },
    Argon2,
};
use base64::{engine::general_purpose::STANDARD as BASE64, Engine as _};
use rand::RngCore;

const SALT_SIZE: usize = 32;
const NONCE_SIZE: usize = 12;
const KEY_SIZE: usize = 32;

pub struct CryptoManager {
    cipher: Aes256Gcm,
}

impl CryptoManager {
    pub fn new(master_password: &str, salt: &[u8]) -> Result<Self> {
        let key = Self::derive_key(master_password, salt)?;
        let cipher = Aes256Gcm::new(&key);
        Ok(Self { cipher })
    }

    fn derive_key(password: &str, salt: &[u8]) -> Result<Key<Aes256Gcm>> {
        let salt_string =
            SaltString::encode_b64(salt).map_err(|e| anyhow!("Failed to encode salt: {}", e))?;

        let argon2 = Argon2::default();
        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt_string)
            .map_err(|e| anyhow!("Failed to hash password: {}", e))?;

        let hash = password_hash
            .hash
            .ok_or_else(|| anyhow!("Failed to get hash bytes"))?;
        let hash_bytes = hash.as_bytes();

        // Use first 32 bytes of the hash as the key
        let mut key_bytes = [0u8; KEY_SIZE];
        key_bytes.copy_from_slice(&hash_bytes[..KEY_SIZE]);

        Ok(Key::<Aes256Gcm>::from(key_bytes))
    }

    pub fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>> {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // Combine nonce and ciphertext
        let mut result = Vec::with_capacity(NONCE_SIZE + ciphertext.len());
        result.extend_from_slice(nonce.as_slice());
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    pub fn decrypt(&self, encrypted_data: &[u8]) -> Result<Vec<u8>> {
        if encrypted_data.len() < NONCE_SIZE {
            return Err(anyhow!("Encrypted data too short"));
        }

        let nonce_bytes = &encrypted_data[..NONCE_SIZE];
        let ciphertext = &encrypted_data[NONCE_SIZE..];

        let nonce = Nonce::from_slice(nonce_bytes);
        let plaintext = self
            .cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        Ok(plaintext)
    }

    pub fn generate_salt() -> Vec<u8> {
        let mut salt = vec![0u8; SALT_SIZE];
        rand::thread_rng().fill_bytes(&mut salt);
        salt
    }

    #[allow(dead_code)]
    pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
        let parsed_hash =
            PasswordHash::new(hash).map_err(|e| anyhow!("Failed to parse hash: {}", e))?;

        Ok(Argon2::default()
            .verify_password(password.as_bytes(), &parsed_hash)
            .is_ok())
    }

    #[allow(dead_code)]
    pub fn hash_password(password: &str) -> Result<String> {
        let salt = SaltString::generate(&mut ArgonOsRng);
        let argon2 = Argon2::default();

        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| anyhow!("Failed to hash password: {}", e))?;

        Ok(password_hash.to_string())
    }
}

#[allow(dead_code)]
pub fn secure_random_bytes(size: usize) -> Vec<u8> {
    let mut bytes = vec![0u8; size];
    rand::thread_rng().fill_bytes(&mut bytes);
    bytes
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_crypto_manager_creation() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();

        let crypto_manager = CryptoManager::new(password, &salt);
        assert!(crypto_manager.is_ok());
    }

    #[test]
    fn test_encrypt_decrypt_roundtrip() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(password, &salt).unwrap();

        let original_data = b"Hello, World! This is a test message.";

        // Encrypt the data
        let encrypted = crypto_manager.encrypt(original_data).unwrap();
        assert_ne!(encrypted, original_data);
        assert!(encrypted.len() > original_data.len()); // Should be larger due to nonce

        // Decrypt the data
        let decrypted = crypto_manager.decrypt(&encrypted).unwrap();
        assert_eq!(decrypted, original_data);
    }

    #[test]
    fn test_encrypt_different_outputs() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(password, &salt).unwrap();

        let data = b"test data";

        // Encrypt the same data twice
        let encrypted1 = crypto_manager.encrypt(data).unwrap();
        let encrypted2 = crypto_manager.encrypt(data).unwrap();

        // Should be different due to random nonces
        assert_ne!(encrypted1, encrypted2);

        // But both should decrypt to the same original data
        assert_eq!(crypto_manager.decrypt(&encrypted1).unwrap(), data);
        assert_eq!(crypto_manager.decrypt(&encrypted2).unwrap(), data);
    }

    #[test]
    fn test_decrypt_invalid_data() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(password, &salt).unwrap();

        // Test with data too short
        let short_data = vec![0u8; 5];
        assert!(crypto_manager.decrypt(&short_data).is_err());

        // Test with corrupted data
        let mut encrypted = crypto_manager.encrypt(b"test data").unwrap();
        encrypted[15] ^= 1; // Flip a bit
        assert!(crypto_manager.decrypt(&encrypted).is_err());
    }

    #[test]
    fn test_different_passwords_different_keys() {
        let salt = CryptoManager::generate_salt();
        let crypto1 = CryptoManager::new("password1", &salt).unwrap();
        let crypto2 = CryptoManager::new("password2", &salt).unwrap();

        let data = b"test data";
        let encrypted1 = crypto1.encrypt(data).unwrap();

        // Should fail to decrypt with different password
        assert!(crypto2.decrypt(&encrypted1).is_err());
    }

    #[test]
    fn test_salt_generation() {
        let salt1 = CryptoManager::generate_salt();
        let salt2 = CryptoManager::generate_salt();

        assert_eq!(salt1.len(), SALT_SIZE);
        assert_eq!(salt2.len(), SALT_SIZE);
        assert_ne!(salt1, salt2); // Should be different
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password";
        let hash = CryptoManager::hash_password(password).unwrap();

        assert!(!hash.is_empty());
        assert!(CryptoManager::verify_password(password, &hash).unwrap());
        assert!(!CryptoManager::verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_secure_random_bytes() {
        let bytes1 = secure_random_bytes(32);
        let bytes2 = secure_random_bytes(32);

        assert_eq!(bytes1.len(), 32);
        assert_eq!(bytes2.len(), 32);
        assert_ne!(bytes1, bytes2); // Should be different
    }

    #[test]
    fn test_empty_data_encryption() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(password, &salt).unwrap();

        let empty_data = b"";
        let encrypted = crypto_manager.encrypt(empty_data).unwrap();
        let decrypted = crypto_manager.decrypt(&encrypted).unwrap();

        assert_eq!(decrypted, empty_data);
    }

    #[test]
    fn test_large_data_encryption() {
        let password = "test_password";
        let salt = CryptoManager::generate_salt();
        let crypto_manager = CryptoManager::new(password, &salt).unwrap();

        // Test with 1MB of data
        let large_data = vec![42u8; 1024 * 1024];
        let encrypted = crypto_manager.encrypt(&large_data).unwrap();
        let decrypted = crypto_manager.decrypt(&encrypted).unwrap();

        assert_eq!(decrypted, large_data);
    }
}

pub fn encode_base64(data: &[u8]) -> String {
    BASE64.encode(data)
}

pub fn decode_base64(data: &str) -> Result<Vec<u8>> {
    BASE64
        .decode(data)
        .map_err(|e| anyhow!("Failed to decode base64: {}", e))
}
