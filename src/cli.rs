use anyhow::Result;
use chrono::Utc;
use clap::Command;
use console::style;
use dialoguer::{Confirm, Input, Password, Select};
use indicatif::{ProgressBar, ProgressStyle};
use std::io::{stdout, Write};
use std::path::Path;
use std::thread;
use std::time::Duration;
// Note: We're using simple ASCII animation instead of the throbber crate for simplicity

#[path = "ui.rs"]
mod ui;

use crate::models::{PasswordEntry, FileEntry, Vault};
use crate::storage::VaultStorage;
use crate::utils::{
    format_file_size, generate_csv_data, generate_strong_password, parse_csv_data,
    validate_password_strength,
};

const DEFAULT_VAULT_PATH: &str = "vault.encrypted";

// Enhanced throbber animation function - no emojis, pure ASCII
fn animate_throbbers(count: usize, prefix: &str) {
    use rand::Rng;
    let mut rng = rand::thread_rng();

    // Simple ASCII characters for animation
    let ascii_chars = vec![
        "|", "/", "-", "\\"
    ];

    print!("{}", prefix);

    for i in 0..count {
        let char_idx = rng.gen_range(0..ascii_chars.len());
        let frame = ascii_chars[char_idx];

        // Cycle through different colors
        let styled_frame = match i % 7 {
            0 => style(frame).yellow().bold(),
            1 => style(frame).cyan().bold(),
            2 => style(frame).magenta().bold(),
            3 => style(frame).blue().bold(),
            4 => style(frame).green().bold(),
            5 => style(frame).red().bold(),
            _ => style(frame).white().bold(),
        };

        print!("{}", styled_frame);
        stdout().flush().unwrap();

        // Dynamic timing patterns
        let delay = match i % 5 {
            0 => 120,  // Slow start
            1 => 40,   // Quick
            2 => 80,   // Medium
            3 => 30,   // Very quick
            _ => 60,   // Standard
        };
        thread::sleep(Duration::from_millis(delay));

        // Dramatic pauses at key moments
        if i == count / 4 || i == count / 2 || i == (count * 3) / 4 {
            thread::sleep(Duration::from_millis(150));
        }
    }

    // ASCII finale - only basic characters
    let finale_chars = ["*", ".", "+"];
    for finale in finale_chars.iter() {
        print!(" {}", style(finale).bold().yellow());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(200));
    }
}

// ASCII-only sparkle animation for maximum compatibility
#[allow(dead_code)]
fn animate_ascii_sparkles(count: usize, prefix: &str) {
    use rand::Rng;
    let mut rng = rand::thread_rng();

    // Pure ASCII characters that work everywhere
    let sparkle_chars = vec![
        "*", ".", "+", "-", "=", "|", "/", "\\"
    ];

    print!("{}", prefix);

    for i in 0..count {
        let sparkle = sparkle_chars[rng.gen_range(0..sparkle_chars.len())];

        // Cycle through different colors
        let styled_sparkle = match i % 7 {
            0 => style(sparkle).yellow().bold(),
            1 => style(sparkle).cyan().bold(),
            2 => style(sparkle).magenta().bold(),
            3 => style(sparkle).blue().bold(),
            4 => style(sparkle).green().bold(),
            5 => style(sparkle).red().bold(),
            _ => style(sparkle).white().bold(),
        };

        print!("{}", styled_sparkle);
        stdout().flush().unwrap();

        // Dynamic timing patterns
        let delay = match i % 5 {
            0 => 120,  // Slow start
            1 => 50,   // Quick
            2 => 80,   // Medium
            3 => 30,   // Very quick
            _ => 70,   // Standard
        };
        thread::sleep(Duration::from_millis(delay));

        // Dramatic pauses at key moments
        if i == count / 4 || i == count / 2 || i == (count * 3) / 4 {
            thread::sleep(Duration::from_millis(150));
        }
    }

    // ASCII finale
    print!(" {} {} {}",
        style("*").bold().yellow(),
        style("+").bold().cyan(),
        style("*").bold().yellow()
    );
    stdout().flush().unwrap();
    thread::sleep(Duration::from_millis(200));
}

// Pulsing text effect
#[allow(dead_code)]
fn animate_pulsing_text(text: &str, pulses: usize) {
    for i in 0..pulses {
        print!("\r{}", style(text).bold().red());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(300));

        if i < pulses - 1 {
            print!("\r{}", " ".repeat(text.len()));
            stdout().flush().unwrap();
            thread::sleep(Duration::from_millis(200));
        }
    }
}

// Display the full CIPHERVAULT banner with animation
#[allow(dead_code)]
fn display_full_banner() {
    let banner_lines = vec![
        "╔═══════════════════════════════════════════════════════════════════════════════════╗",
        "║ ██████╗██╗██████╗ ██╗  ██╗███████╗██████╗ ██╗   ██╗ █████╗ ██╗   ██╗██╗  ████████╗║",
        "║██╔════╝██║██╔══██╗██║  ██║██╔════╝██╔══██╗██║   ██║██╔══██╗██║   ██║██║  ╚══██╔══╝║",
        "║██║     ██║██████╔╝███████║█████╗  ██████╔╝██║   ██║███████║██║   ██║██║     ██║   ║",
        "║██║     ██║██╔═══╝ ██╔══██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══██║██║   ██║██║     ██║   ║",
        "║╚██████╗██║██║     ██║  ██║███████╗██║  ██║ ╚████╔╝ ██║  ██║╚██████╔╝███████╗██║   ║",
        "║ ╚═════╝╚═╝╚═╝     ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝   ║",
        "╚═══════════════════════════════════════════════════════════════════════════════════╝",
    ];

    // Animate banner line by line
    for line in banner_lines {
        println!("    {}", style(line).bold().cyan());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(80));
    }
}

// Display a simple banner for quick operations
#[allow(dead_code)]
fn display_simple_banner() {
    println!("{}", style("🔐 CipherVault - The Most Secure Password Vault").bold().cyan());
    println!("{}", style("===============================================").cyan());
}

// Spinner styles
fn create_spinner(message: &str) -> ProgressBar {
    let spinner = ProgressBar::new_spinner();
    spinner.set_style(
        ProgressStyle::default_spinner()
            .template("{spinner:.green} {wide_msg}")
            .unwrap()
            .tick_chars("⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"),
    );
    spinner.set_message(message.to_string());
    spinner
}

fn create_progress_bar(len: u64, message: &str) -> ProgressBar {
    let pb = ProgressBar::new(len);
    pb.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} {msg}")
            .unwrap()
            .progress_chars("#>-"),
    );
    pb.set_message(message.to_string());
    pb
}

pub async fn show_help_anim(mut cmd: Command) {
    // Multi-stage loading animation with throbbers only
    let mut throbber = ui::ProgressThrobber::new("Initializing help system");

    // Stage 1: Initialize
    for _ in 0..8 {
        throbber.tick();
        thread::sleep(Duration::from_millis(60));
    }
    throbber.finish_with_message("System initialized");

    // Stage 2: Loading components
    let mut throbber = ui::ProgressThrobber::new("Loading help components");
    for _ in 0..12 {
        throbber.tick();
        thread::sleep(Duration::from_millis(40));
    }
    throbber.finish_with_message("Components loaded");

    // Stage 3: Preparing interface
    let mut throbber = ui::ProgressThrobber::new("Preparing interface");
    for _ in 0..15 {
        throbber.tick();
        thread::sleep(Duration::from_millis(35));
    }
    throbber.finish_with_message("Interface ready!");

    // Stage 4: Final preparations
    let mut throbber = ui::ProgressThrobber::new("Launching help experience");
    for _ in 0..8 {
        throbber.tick();
        thread::sleep(Duration::from_millis(50));
    }
    throbber.finish_with_message("Ready to help!");

    // Dramatic pause
    thread::sleep(Duration::from_millis(300));

    // Clear screen with animation
    print!("\x1B[2J\x1B[1;1H");

    // Animated banner reveal
    let banner_lines = vec![
        "╔═══════════════════════════════════════════════════════════════════════════════════╗",
        "║ ██████╗██╗██████╗ ██╗  ██╗███████╗██████╗ ██╗   ██╗ █████╗ ██╗   ██╗██╗  ████████╗║",
        "║██╔════╝██║██╔══██╗██║  ██║██╔════╝██╔══██╗██║   ██║██╔══██╗██║   ██║██║  ╚══██╔══╝║",
        "║██║     ██║██████╔╝███████║█████╗  ██████╔╝██║   ██║███████║██║   ██║██║     ██║   ║",
        "║██║     ██║██╔═══╝ ██╔══██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══██║██║   ██║██║     ██║   ║",
        "║╚██████╗██║██║     ██║  ██║███████╗██║  ██║ ╚████╔╝ ██║  ██║╚██████╔╝███████╗██║   ║",
        "║ ╚═════╝╚═╝╚═╝     ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝   ║",
        "╚═══════════════════════════════════════════════════════════════════════════════════╝",
    ];

    // Animate banner line by line
    for line in banner_lines {
        println!("    {}", style(line).bold().cyan());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(80));
    }

    // Enhanced animated throbber effect
    animate_throbbers(12, "    ");
    println!("\n");

    // Animated version info
    let version_text = format!("Version: {}", env!("CARGO_PKG_VERSION"));
    print!("  >> ");
    for char in version_text.chars() {
        print!("{}", style(char).green().bold());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(20));
    }
    println!();

    let desc_text = "The most secure password vault with military-grade encryption";
    print!("  >> ");
    for char in desc_text.chars() {
        print!("{}", style(char).dim());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(15));
    }
    println!("\n");

    // Animated section loading
    let mut throbber = ui::ProgressThrobber::new("Loading help sections");
    for _ in 0..6 {
        throbber.tick();
        thread::sleep(Duration::from_millis(80));
    }
    throbber.finish_with_message("Help sections ready!");

    // Get the help text and split into sections
    let help_text = cmd.render_help().to_string();
    let sections = help_text.split("\n\n").collect::<Vec<_>>();

    // Print each section with enhanced formatting and animations
    for (i, section) in sections.iter().enumerate() {
        if section.trim().is_empty() {
            continue;
        }

        let (header, content) = if let Some((h, c)) = section.split_once('\n') {
            (h.trim(), c.trim())
        } else {
            ("", section.trim())
        };

        // Skip empty sections
        if content.is_empty() {
            continue;
        }

        // Animated section reveal
        if !header.is_empty() {
            // Section loading animation with throbber
            let section_markers = [">", ">>", ">>>", ">>>>", ">>>>>"];
            let marker = section_markers.get(i % section_markers.len()).unwrap_or(&">");

            print!("\n{} ", style(marker).bold().cyan());
            for char in header.chars() {
                print!("{}", style(char).bold().cyan().underlined());
                stdout().flush().unwrap();
                thread::sleep(Duration::from_millis(25));
            }
            println!();

            // Animated underline
            let underline = "─".repeat(header.len() + 2);
            for char in underline.chars() {
                print!("{}", style(char).cyan());
                stdout().flush().unwrap();
                thread::sleep(Duration::from_millis(10));
            }
            println!();
        }

        // Enhanced content rendering with better animations
        for (line_idx, line) in content.lines().enumerate() {
            let line = line.trim();
            if line.is_empty() {
                continue;
            }

            // Add enhanced bullet points for command lists
            let mut formatted_line = line.to_string();
            if line.starts_with("  ") && !line.starts_with("    ") {
                formatted_line = format!("  {} {}", style("*").green().bold(), line.trim_start());
            }

            // Staggered line appearance
            thread::sleep(Duration::from_millis(30));

            // Print with enhanced typewriter effect
            print!("  ");
            for char in formatted_line.chars() {
                let styled_char = if char.is_alphanumeric() {
                    style(char).white().to_string()
                } else {
                    style(char).dim().to_string()
                };

                print!("{}", styled_char);
                stdout().flush().unwrap();

                // Variable speed typewriter effect
                let delay = if char.is_whitespace() {
                    0
                } else if char.is_alphanumeric() {
                    2
                } else {
                    1
                };
                thread::sleep(Duration::from_millis(delay));
            }
            println!();

            // Brief pause between lines
            if line_idx % 3 == 2 {
                thread::sleep(Duration::from_millis(50));
            }
        }

        // Section completion animation
        if i < sections.len() - 1 {
            thread::sleep(Duration::from_millis(100));
        }
    }

    // Animated examples section loading
    let mut throbber = ui::ProgressThrobber::new("Preparing examples");
    for _ in 0..5 {
        throbber.tick();
        thread::sleep(Duration::from_millis(60));
    }
    throbber.finish_with_message("Examples ready!");

    // Animated examples header
    print!("\n>> ");
    let examples_header = "EXAMPLES";
    for char in examples_header.chars() {
        print!("{}", style(char).bold().cyan().underlined());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(40));
    }
    println!();

    // Animated underline
    let underline = "─".repeat(examples_header.len() + 2);
    for char in underline.chars() {
        print!("{}", style(char).cyan());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(15));
    }
    println!();

    let examples = [
        (
            "Add a password",
            "ciphervault add-password --title GitHub --username <EMAIL>",
        ),
        ("List all entries", "ciphervault list"),
        ("Search for entries", "ciphervault search github"),
        (
            "Add a file",
            "ciphervault add-file --file-path secret.txt",
        ),
        ("Export to CSV", "ciphervault export --output backup.csv"),
        ("Change password", "ciphervault change-password"),
    ];

    for (_i, (desc, cmd)) in examples.iter().enumerate() {
        // Staggered example appearance
        thread::sleep(Duration::from_millis(200));

        // Animated description
        print!("  ");
        for char in desc.chars() {
            print!("{}", style(char).bold().white());
            stdout().flush().unwrap();
            thread::sleep(Duration::from_millis(20));
        }
        println!();

        // Animated prompt
        print!("    ");
        for char in "$ ".chars() {
            print!("{}", style(char).dim().bold());
            stdout().flush().unwrap();
            thread::sleep(Duration::from_millis(50));
        }

        // Animated command
        for char in cmd.chars() {
            let styled_char = if char == '-' {
                style(char).yellow().to_string()
            } else if char.is_alphabetic() {
                style(char).green().to_string()
            } else {
                style(char).white().to_string()
            };

            print!("{}", styled_char);
            stdout().flush().unwrap();
            thread::sleep(Duration::from_millis(15));
        }
        println!("\n");
    }

    // Animated security section
    let mut throbber = ui::ProgressThrobber::new("Loading security tips");
    for _ in 0..4 {
        throbber.tick();
        thread::sleep(Duration::from_millis(70));
    }
    throbber.finish_with_message("Security tips ready!");

    // Security note with animation
    print!("\n>> ");
    for char in "SECURITY NOTE".chars() {
        print!("{}", style(char).bold().red());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(30));
    }
    println!();

    let security_tips = [
        "Always lock your terminal when not in use",
        "Never share your master password or vault file",
        "Keep regular backups of your encrypted vault",
    ];

    for tip in security_tips.iter() {
        print!("  ");
        for char in tip.chars() {
            print!("{}", style(char).dim());
            stdout().flush().unwrap();
            thread::sleep(Duration::from_millis(10));
        }
        println!();
        thread::sleep(Duration::from_millis(100));
    }

    // Animated final messages
    thread::sleep(Duration::from_millis(300));

    print!("\n>> ");
    let tip_text = "Tip: Run 'ciphervault <command> --help' for more info";
    for char in tip_text.chars() {
        print!("{}", style(char).dim());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(8));
    }
    println!();

    // Grand finale animation
    thread::sleep(Duration::from_millis(200));
    let finale_text = "Happy and secure password managing!";
    print!("\n>> ");
    for char in finale_text.chars() {
        print!("{}", style(char).bold().green());
        stdout().flush().unwrap();
        thread::sleep(Duration::from_millis(50));
    }
    println!();

    // Closing throbber animation
    thread::sleep(Duration::from_millis(300));
    animate_throbbers(8, "  ");
    println!("\n");
}

pub async fn init_vault(
    vault_path: &str,
    master_password: Option<String>,
    force: bool,
) -> Result<()> {
    println!("{}", style("🔐 Initializing new vault...").bold().green());

    let master_password = if let Some(mp) = master_password {
        mp
    } else {
        Password::new()
            .with_prompt("Enter master password")
            .with_confirmation("Confirm master password", "Passwords don't match")
            .interact()?
    };

    // Validate password strength
    let (is_strong, issues) = validate_password_strength(&master_password);
    if !is_strong {
        println!("{}", style("⚠️  Password strength issues:").yellow());
        for issue in issues {
            println!("  • {}", issue);
        }

        if !force
            && !Confirm::new()
                .with_prompt("Continue with weak password?")
                .default(false)
                .interact()?
        {
            return Ok(());
        }
    }

    let spinner = create_spinner("Creating vault structure...");
    let mut storage = VaultStorage::new(vault_path);
    storage.initialize(&master_password)?;
    spinner.finish_with_message("✅ Vault structure created");

    let spinner = create_spinner("Generating encryption keys...");
    std::thread::sleep(std::time::Duration::from_millis(800));
    spinner.finish_with_message("✅ Encryption keys generated");

    let spinner = create_spinner("Securing vault data...");
    std::thread::sleep(std::time::Duration::from_millis(600));
    spinner.finish_with_message("✅ Vault data secured");

    println!(
        "{}",
        style("✅ Vault initialized successfully!").bold().green()
    );
    println!("Vault file: {}", vault_path);

    Ok(())
}

pub async fn add_password(
    title: String,
    username: String,
    password: Option<String>,
    url: Option<String>,
    notes: Option<String>,
    master_password: Option<String>,
    vault_path: &str,
    tags: Option<Vec<String>>,
    category: String,
    favorite: bool,
    copy: bool,
) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(vault_path);
    let master_password = if let Some(mp) = master_password {
        mp
    } else {
        Password::new()
            .with_prompt("Enter master password")
            .interact()?
    };

    let mut vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    let password = if let Some(pwd) = password {
        pwd
    } else {
        let options = vec!["Enter manually", "Generate strong password"];
        let choice = Select::new()
            .with_prompt("Password option")
            .items(&options)
            .default(0)
            .interact()?;

        match choice {
            0 => Password::new().with_prompt("Enter password").interact()?,
            1 => {
                let length: usize = Input::new()
                    .with_prompt("Password length")
                    .default(16)
                    .interact()?;
                let spinner = create_spinner("Generating secure password...");
                let generated = generate_strong_password(length);
                std::thread::sleep(std::time::Duration::from_millis(500));
                spinner.finish_with_message("✅ Password generated");
                println!("Generated password: {}", style(&generated).bold());
                generated
            }
            _ => unreachable!(),
        }
    };

    let tags = if let Some(t) = tags {
        t
    } else {
        let tags_input: String = Input::new()
            .with_prompt("Tags (comma-separated)")
            .allow_empty(true)
            .interact()?;

        tags_input
            .split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect()
    };

    let spinner = create_spinner("Encrypting and saving password...");
    let entry = PasswordEntry {
        id: uuid::Uuid::new_v4(),
        title,
        username,
        password: Some(password.clone()),
        url,
        notes,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        tags,
        category,
        favorite,
    };

    vault.add_password(entry);
    storage.save_vault(&vault)?;
    spinner.finish_with_message("✅ Password saved securely");

    println!(
        "{}",
        style("Password added successfully!").bold().green()
    );

    // Copy to clipboard if requested
    if copy {
        use crate::clipboard::copy_password_secure;
        if let Err(e) = copy_password_secure(&password) {
            println!("Warning: Failed to copy to clipboard: {}", e);
        }
    }

    Ok(())
}

pub async fn add_file(file_path: String, description: Option<String>) -> Result<()> {
    if !Path::new(&file_path).exists() {
        return Err(anyhow::anyhow!("File does not exist: {}", file_path));
    }

    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let mut vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    let description = description.unwrap_or_else(|| {
        Input::new()
            .with_prompt("Description (optional)")
            .allow_empty(true)
            .interact()
            .unwrap_or_default()
    });

    let tags_input: String = Input::new()
        .with_prompt("Tags (comma-separated)")
        .allow_empty(true)
        .interact()?;

    let tags: Vec<String> = tags_input
        .split(',')
        .map(|s| s.trim().to_string())
        .filter(|s| !s.is_empty())
        .collect();

    // Get file size for progress bar
    let file_size = std::fs::metadata(&file_path)?.len();
    let progress_bar = create_progress_bar(file_size, "Encrypting file...");

    let mut file_entry = storage.add_file(&file_path, Some(description)).await?;
    file_entry.tags = tags;

    // Simulate progress for file encryption
    let chunk_size = file_size / 100;
    for i in 0..100 {
        progress_bar.set_position(i * chunk_size);
        std::thread::sleep(std::time::Duration::from_millis(10));
    }
    progress_bar.finish_with_message("✅ File encrypted and saved");

    vault.add_file(file_entry);
    storage.save_vault(&vault)?;

    println!("{}", style("File added successfully!").bold().green());
    println!("File size: {}", format_file_size(file_size));

    Ok(())
}

pub async fn list_entries(
    master_password: Option<String>,
    vault_path: &str,
    category: Option<String>,
    favorites: bool,
    stats: bool
) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(vault_path);
    let master_password = if let Some(mp) = master_password {
        mp
    } else {
        Password::new()
            .with_prompt("Enter master password")
            .interact()?
    };

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    // Show category statistics if requested
    if stats {
        show_category_statistics(&vault);
        return Ok(());
    }

    // Filter entries based on criteria
    let filtered_passwords: Vec<&PasswordEntry> = vault.passwords.iter()
        .filter(|entry| {
            if let Some(ref cat) = category {
                if entry.category != *cat {
                    return false;
                }
            }
            if favorites && !entry.favorite {
                return false;
            }
            true
        })
        .collect();

    let filtered_files: Vec<&FileEntry> = vault.files.iter()
        .filter(|entry| {
            if let Some(ref cat) = category {
                if entry.category != *cat {
                    return false;
                }
            }
            if favorites && !entry.favorite {
                return false;
            }
            true
        })
        .collect();

    // Display header with filter info
    let mut header = "Entries".to_string();
    if let Some(ref cat) = category {
        header = format!("{} in category '{}'", header, cat);
    }
    if favorites {
        header = format!("{} (favorites only)", header);
    }

    println!("{}", style(format!("🔑 Password {}:", header)).bold().cyan());
    println!("{}", "=".repeat(50));

    for entry in &filtered_passwords {
        let title_display = if entry.favorite {
            format!("⭐ {}", entry.title)
        } else {
            entry.title.clone()
        };

        println!("📝 {}", style(&title_display).bold());
        println!("   Username: {}", entry.username);
        println!("   URL: {}", entry.url.as_deref().unwrap_or("N/A"));
        println!("   Category: {}", style(&entry.category).cyan());
        println!("   Created: {}", entry.created_at.format("%Y-%m-%d %H:%M"));
        if !entry.tags.is_empty() {
            println!("   Tags: {}", entry.tags.join(", "));
        }
        println!();
    }

    println!("{}", style(format!("📁 File {}:", header)).bold().cyan());
    println!("{}", "=".repeat(50));

    for entry in &filtered_files {
        let filename_display = if entry.favorite {
            format!("⭐ {}", entry.filename)
        } else {
            entry.filename.clone()
        };

        println!("📄 {}", style(&filename_display).bold());
        println!("   Original: {}", entry.original_path);
        println!("   Size: {}", format_file_size(entry.file_size));
        println!("   Type: {}", entry.mime_type);
        println!("   Category: {}", style(&entry.category).cyan());
        if let Some(desc) = &entry.description {
            println!("   Description: {}", desc);
        }
        println!("   Created: {}", entry.created_at.format("%Y-%m-%d %H:%M"));
        if !entry.tags.is_empty() {
            println!("   Tags: {}", entry.tags.join(", "));
        }
        println!();
    }

    println!(
        "{}",
        style(format!(
            "Showing: {} passwords, {} files (Total: {} passwords, {} files)",
            filtered_passwords.len(),
            filtered_files.len(),
            vault.passwords.len(),
            vault.files.len()
        ))
        .dim()
    );

    Ok(())
}

pub async fn search_entries(query: &str) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    let spinner = create_spinner("Searching entries...");
    let password_results = vault.search_passwords(query);
    let file_results = vault.search_files(query);
    spinner.finish_with_message("✅ Search completed");

    println!(
        "{}",
        style(format!("🔍 Search results for '{}':", query))
            .bold()
            .cyan()
    );
    println!("{}", "=".repeat(50));

    if password_results.is_empty() && file_results.is_empty() {
        println!("{}", style("No results found").dim());
        return Ok(());
    }

    if !password_results.is_empty() {
        println!("{}", style("🔑 Password Entries:").bold().green());
        for entry in &password_results {
            println!("📝 {}", style(&entry.title).bold());
            println!("   Username: {}", entry.username);
            println!("   URL: {}", entry.url.as_deref().unwrap_or("N/A"));
            println!();
        }
    }

    if !file_results.is_empty() {
        println!("{}", style("📁 File Entries:").bold().green());
        for entry in &file_results {
            println!("📄 {}", style(&entry.filename).bold());
            println!("   Original: {}", entry.original_path);
            println!("   Size: {}", format_file_size(entry.file_size));
            println!();
        }
    }

    println!(
        "{}",
        style(format!(
            "Found: {} passwords, {} files",
            password_results.len(),
            file_results.len()
        ))
        .dim()
    );

    Ok(())
}

pub async fn export_entries(output: &str) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    let spinner = create_spinner("Generating CSV data...");
    let csv_data = generate_csv_data(&vault.passwords)?;
    spinner.finish_with_message("✅ CSV data generated");

    let spinner = create_spinner("Writing to file...");
    std::fs::write(output, csv_data)?;
    spinner.finish_with_message("✅ File written");

    println!(
        "{}",
        style(format!(
            "✅ Exported {} entries to {}",
            vault.passwords.len(),
            output
        ))
        .bold()
        .green()
    );

    Ok(())
}

pub async fn import_entries(input: &str) -> Result<()> {
    if !Path::new(input).exists() {
        return Err(anyhow::anyhow!("File does not exist: {}", input));
    }

    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let mut vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    let spinner = create_spinner("Reading CSV file...");
    let csv_content = std::fs::read_to_string(input)?;
    let csv_rows = parse_csv_data(&csv_content)?;
    spinner.finish_with_message("✅ CSV file parsed");

    let progress_bar = create_progress_bar(csv_rows.len() as u64, "Importing entries...");

    let mut imported_count = 0;
    for (i, row) in csv_rows.into_iter().enumerate() {
        let tags: Vec<String> = row
            .tags
            .unwrap_or_default()
            .split(',')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();

        let entry = PasswordEntry {
            id: uuid::Uuid::new_v4(),
            title: row.title,
            username: row.username,
            password: Some(row.password),
            url: row.url,
            notes: row.notes,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            tags,
            category: "General".to_string(),
            favorite: false,
        };

        vault.add_password(entry);
        imported_count += 1;
        progress_bar.set_position((i + 1) as u64);
        std::thread::sleep(std::time::Duration::from_millis(50));
    }

    progress_bar.finish_with_message("✅ Entries imported");

    let spinner = create_spinner("Saving vault...");
    storage.save_vault(&vault)?;
    spinner.finish_with_message("✅ Vault saved");

    println!(
        "{}",
        style(format!(
            "✅ Imported {} entries from {}",
            imported_count, input
        ))
        .bold()
        .green()
    );

    Ok(())
}

pub async fn change_master_password() -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let current_password = Password::new()
        .with_prompt("Enter current master password")
        .interact()?;

    // Verify current password by trying to load the vault
    let _vault = storage.load(&current_password)?;
    spinner.finish_with_message("✅ Current password verified");

    let new_password = Password::new()
        .with_prompt("Enter new master password")
        .with_confirmation("Confirm new master password", "Passwords don't match")
        .interact()?;

    // Validate new password strength
    let (is_strong, issues) = validate_password_strength(&new_password);
    if !is_strong {
        println!("{}", style("⚠️  Password strength issues:").yellow());
        for issue in issues {
            println!("  • {}", issue);
        }

        if !Confirm::new()
            .with_prompt("Continue with weak password?")
            .default(false)
            .interact()?
        {
            return Ok(());
        }
    }

    let spinner = create_spinner("Re-encrypting vault with new password...");
    storage.change_master_password(&new_password)?;
    spinner.finish_with_message("✅ Vault re-encrypted");

    println!(
        "{}",
        style("✅ Master password changed successfully!")
            .bold()
            .green()
    );

    Ok(())
}

pub async fn extract_file(filename: &str, output: &str) -> Result<()> {
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let vault = storage.load(&master_password)?;
    let file_entry = vault
        .files
        .iter()
        .find(|f| f.filename == filename)
        .ok_or_else(|| anyhow::anyhow!("File not found in vault"))?;

    let spinner = create_spinner("Decrypting file...");
    storage.extract_file(file_entry, output).await?;
    spinner.finish_with_message("✅ File extracted and decrypted");

    println!("File saved to: {}", output);
    Ok(())
}

// Category management functions
pub async fn list_categories() -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    println!("{}", style("📂 Categories:").bold().cyan());
    println!("{}", "=".repeat(50));

    for category in vault.get_categories() {
        let icon = category.icon.as_deref().unwrap_or("📁");
        println!("{} {}", icon, style(&category.name).bold());
        if let Some(desc) = &category.description {
            println!("   Description: {}", desc);
        }
        println!("   Created: {}", category.created_at.format("%Y-%m-%d %H:%M"));
        println!();
    }

    println!(
        "{}",
        style(format!("Total: {} categories", vault.get_categories().len())).dim()
    );

    Ok(())
}

pub async fn add_category(name: String, description: Option<String>, icon: Option<String>) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let mut vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    match vault.add_category(name.clone(), description, icon) {
        Ok(_) => {
            let spinner = create_spinner("Saving vault...");
            storage.save_vault(&vault)?;
            spinner.finish_with_message("✅ Category saved");

            println!(
                "{}",
                style(format!("Category '{}' added successfully!", name)).bold().green()
            );
        }
        Err(e) => {
            println!("{}", style(format!("Error: {}", e)).red());
        }
    }

    Ok(())
}

pub async fn show_category_stats() -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(DEFAULT_VAULT_PATH);
    let master_password = Password::new()
        .with_prompt("Enter master password")
        .interact()?;

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    show_category_statistics(&vault);
    Ok(())
}

fn show_category_statistics(vault: &Vault) {
    println!("{}", style("📊 Category Statistics:").bold().cyan());
    println!("{}", "=".repeat(50));

    let stats = vault.get_category_stats();

    for (category_name, (password_count, file_count)) in stats {
        let total = password_count + file_count;
        if total > 0 {
            let category = vault.get_category_by_name(&category_name);
            let icon = category
                .and_then(|c| c.icon.as_deref())
                .unwrap_or("📁");

            println!("{} {}", icon, style(&category_name).bold());
            println!("   Passwords: {}", password_count);
            println!("   Files: {}", file_count);
            println!("   Total: {}", style(total.to_string()).bold());
            println!();
        }
    }
}

// Clipboard functionality
pub async fn copy_password_to_clipboard(
    entry: &str,
    master_password: Option<String>,
    vault_path: &str,
    timeout: u64,
) -> Result<()> {
    let spinner = create_spinner("Loading vault...");
    let mut storage = VaultStorage::new(vault_path);
    let master_password = if let Some(mp) = master_password {
        mp
    } else {
        Password::new()
            .with_prompt("Enter master password")
            .interact()?
    };

    let vault = storage.load(&master_password)?;
    spinner.finish_with_message("✅ Vault loaded");

    // Find the entry by title or ID
    let password_entry = vault.passwords.iter()
        .find(|p| p.title.to_lowercase().contains(&entry.to_lowercase()) || p.id.to_string() == entry);

    if let Some(entry) = password_entry {
        if let Some(password) = &entry.password {
            use crate::clipboard::ClipboardManager;
            let mut clipboard = ClipboardManager::new()?;
            clipboard.copy_with_timeout(password, timeout)?;

            println!(
                "{}",
                style(format!("Password for '{}' copied to clipboard", entry.title))
                    .bold()
                    .green()
            );
        } else {
            println!("{}", style("No password found for this entry").red());
        }
    } else {
        println!("{}", style(format!("Entry '{}' not found", entry)).red());
    }

    Ok(())
}
