use anyhow::Result;
use arboard::Clipboard;
use console::style;
use std::thread;
use std::time::Duration;

pub struct ClipboardManager {
    clipboard: Clipboard,
}

impl ClipboardManager {
    pub fn new() -> Result<Self> {
        let clipboard = Clipboard::new()?;
        Ok(Self { clipboard })
    }

    /// Copy text to clipboard with automatic clearing after timeout
    pub fn copy_with_timeout(&mut self, text: &str, timeout_seconds: u64) -> Result<()> {
        // Copy to clipboard
        self.clipboard.set_text(text)?;
        
        println!(
            "{}",
            style(format!("Copied to clipboard (will clear in {}s)", timeout_seconds))
                .green()
                .bold()
        );

        // Start a background thread to clear clipboard after timeout
        let text_to_clear = text.to_string();
        thread::spawn(move || {
            thread::sleep(Duration::from_secs(timeout_seconds));
            
            // Only clear if the clipboard still contains our text
            if let Ok(mut clipboard) = Clipboard::new() {
                if let Ok(current_content) = clipboard.get_text() {
                    if current_content == text_to_clear {
                        let _ = clipboard.set_text("");
                        println!(
                            "{}",
                            style("Clipboard cleared for security")
                                .dim()
                        );
                    }
                }
            }
        });

        Ok(())
    }


}

/// Copy password to clipboard with secure timeout
pub fn copy_password_secure(password: &str) -> Result<()> {
    let mut clipboard = ClipboardManager::new()?;
    clipboard.copy_with_timeout(password, 30) // 30 second timeout
}


