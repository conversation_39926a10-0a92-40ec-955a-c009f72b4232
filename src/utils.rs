use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;

pub fn guess_mime_type(path: &Path) -> String {
    if let Some(extension) = path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        match ext.as_str() {
            // Images
            "jpg" | "jpeg" => "image/jpeg",
            "png" => "image/png",
            "gif" => "image/gif",
            "bmp" => "image/bmp",
            "webp" => "image/webp",
            "svg" => "image/svg+xml",

            // Videos
            "mp4" => "video/mp4",
            "avi" => "video/x-msvideo",
            "mov" => "video/quicktime",
            "wmv" => "video/x-ms-wmv",
            "flv" => "video/x-flv",
            "webm" => "video/webm",
            "mkv" => "video/x-matroska",

            // Documents
            "pdf" => "application/pdf",
            "doc" => "application/msword",
            "docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "xls" => "application/vnd.ms-excel",
            "xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "ppt" => "application/vnd.ms-powerpoint",
            "pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",

            // Text files
            "txt" => "text/plain",
            "csv" => "text/csv",
            "json" => "application/json",
            "xml" => "application/xml",
            "html" => "text/html",
            "css" => "text/css",
            "js" => "application/javascript",

            // Archives
            "zip" => "application/zip",
            "rar" => "application/vnd.rar",
            "7z" => "application/x-7z-compressed",
            "tar" => "application/x-tar",
            "gz" => "application/gzip",

            // Audio
            "mp3" => "audio/mpeg",
            "wav" => "audio/wav",
            "flac" => "audio/flac",
            "aac" => "audio/aac",
            "ogg" => "audio/ogg",

            _ => "application/octet-stream",
        }
        .to_string()
    } else {
        "application/octet-stream".to_string()
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CsvRow {
    #[serde(rename = "Title")]
    pub title: String,
    #[serde(rename = "Username")]
    pub username: String,
    #[serde(rename = "Password")]
    pub password: String,
    #[serde(rename = "URL")]
    pub url: Option<String>,
    #[serde(rename = "Notes")]
    pub notes: Option<String>,
    #[serde(rename = "Tags")]
    pub tags: Option<String>,
}

pub fn parse_csv_data(csv_data: &str) -> Result<Vec<CsvRow>> {
    let mut reader = csv::Reader::from_reader(csv_data.as_bytes());
    let mut rows = Vec::new();

    for result in reader.deserialize() {
        let row: CsvRow = result?;
        rows.push(row);
    }

    Ok(rows)
}

pub fn generate_csv_data(entries: &[crate::models::PasswordEntry]) -> Result<String> {
    let mut writer = csv::Writer::from_writer(Vec::new());

    for entry in entries {
        writer.serialize(CsvRow {
            title: entry.title.clone(),
            username: entry.username.clone(),
            password: entry.password.clone().unwrap_or_default(),
            url: entry.url.clone(),
            notes: entry.notes.clone(),
            tags: Some(entry.tags.join(", ")),
        })?;
    }

    writer.flush()?;
    let data = writer.into_inner()?;
    Ok(String::from_utf8(data)?)
}

pub fn format_file_size(size: u64) -> String {
    const KB: u64 = 1024;
    const MB: u64 = KB * 1024;
    const GB: u64 = MB * 1024;

    match size {
        0..KB => format!("{} B", size),
        KB..MB => format!("{:.1} KB", size as f64 / KB as f64),
        MB..GB => format!("{:.1} MB", size as f64 / MB as f64),
        _ => format!("{:.1} GB", size as f64 / GB as f64),
    }
}

pub fn generate_strong_password(length: usize) -> String {
    use rand::Rng;
    const CHARS: &[u8] =
        b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?";

    let mut rng = rand::thread_rng();
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARS.len());
            CHARS[idx] as char
        })
        .collect()
}

pub fn validate_password_strength(password: &str) -> (bool, Vec<String>) {
    let mut issues = Vec::new();
    let mut score = 0;

    if password.len() < 8 {
        issues.push("Password must be at least 8 characters long".to_string());
    } else {
        score += 1;
    }

    if password.chars().any(|c| c.is_uppercase()) {
        score += 1;
    } else {
        issues.push("Password should contain at least one uppercase letter".to_string());
    }

    if password.chars().any(|c| c.is_lowercase()) {
        score += 1;
    } else {
        issues.push("Password should contain at least one lowercase letter".to_string());
    }

    if password.chars().any(|c| c.is_numeric()) {
        score += 1;
    } else {
        issues.push("Password should contain at least one number".to_string());
    }

    if password.chars().any(|c| !c.is_alphanumeric()) {
        score += 1;
    } else {
        issues.push("Password should contain at least one special character".to_string());
    }

    let is_strong = score >= 4 && password.len() >= 8;
    (is_strong, issues)
}

#[allow(dead_code)]
pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| {
            if c.is_alphanumeric() || c == '.' || c == '-' || c == '_' {
                c
            } else {
                '_'
            }
        })
        .collect()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::PasswordEntry;
    use chrono::Utc;
    use std::path::Path;
    use uuid::Uuid;

    #[test]
    fn test_guess_mime_type_images() {
        assert_eq!(guess_mime_type(Path::new("test.jpg")), "image/jpeg");
        assert_eq!(guess_mime_type(Path::new("test.jpeg")), "image/jpeg");
        assert_eq!(guess_mime_type(Path::new("test.png")), "image/png");
        assert_eq!(guess_mime_type(Path::new("test.gif")), "image/gif");
        assert_eq!(guess_mime_type(Path::new("test.bmp")), "image/bmp");
        assert_eq!(guess_mime_type(Path::new("test.webp")), "image/webp");
        assert_eq!(guess_mime_type(Path::new("test.svg")), "image/svg+xml");
    }

    #[test]
    fn test_guess_mime_type_videos() {
        assert_eq!(guess_mime_type(Path::new("test.mp4")), "video/mp4");
        assert_eq!(guess_mime_type(Path::new("test.avi")), "video/x-msvideo");
        assert_eq!(guess_mime_type(Path::new("test.mov")), "video/quicktime");
        assert_eq!(guess_mime_type(Path::new("test.wmv")), "video/x-ms-wmv");
        assert_eq!(guess_mime_type(Path::new("test.webm")), "video/webm");
    }

    #[test]
    fn test_guess_mime_type_documents() {
        assert_eq!(guess_mime_type(Path::new("test.pdf")), "application/pdf");
        assert_eq!(guess_mime_type(Path::new("test.doc")), "application/msword");
        assert_eq!(
            guess_mime_type(Path::new("test.docx")),
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        );
        assert_eq!(guess_mime_type(Path::new("test.txt")), "text/plain");
        assert_eq!(guess_mime_type(Path::new("test.json")), "application/json");
    }

    #[test]
    fn test_guess_mime_type_case_insensitive() {
        assert_eq!(guess_mime_type(Path::new("test.JPG")), "image/jpeg");
        assert_eq!(guess_mime_type(Path::new("test.PDF")), "application/pdf");
        assert_eq!(guess_mime_type(Path::new("test.Mp4")), "video/mp4");
    }

    #[test]
    fn test_guess_mime_type_unknown() {
        assert_eq!(
            guess_mime_type(Path::new("test.unknown")),
            "application/octet-stream"
        );
        assert_eq!(
            guess_mime_type(Path::new("test")),
            "application/octet-stream"
        );
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1023), "1023 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1024 * 1024), "1.0 MB");
        assert_eq!(format_file_size(1024 * 1024 * 1024), "1.0 GB");
        assert_eq!(format_file_size(1536 * 1024 * 1024), "1.5 GB");
    }

    #[test]
    fn test_generate_strong_password() {
        let password = generate_strong_password(16);
        assert_eq!(password.len(), 16);

        // Test different lengths
        for length in [8, 12, 16, 24, 32] {
            let pwd = generate_strong_password(length);
            assert_eq!(pwd.len(), length);
        }

        // Test that generated passwords are different
        let pwd1 = generate_strong_password(16);
        let pwd2 = generate_strong_password(16);
        assert_ne!(pwd1, pwd2);
    }

    #[test]
    fn test_validate_password_strength_strong() {
        let (is_strong, issues) = validate_password_strength("MyStr0ng!P@ssw0rd");
        assert!(is_strong);
        assert!(issues.is_empty());
    }

    #[test]
    fn test_validate_password_strength_weak() {
        let (is_strong, issues) = validate_password_strength("weak");
        assert!(!is_strong);
        assert!(!issues.is_empty());
        assert!(issues.iter().any(|i| i.contains("8 characters")));
        assert!(issues.iter().any(|i| i.contains("uppercase")));
        assert!(issues.iter().any(|i| i.contains("number")));
        assert!(issues.iter().any(|i| i.contains("special character")));
    }

    #[test]
    fn test_csv_parsing() {
        let csv_data =
            "Title,Username,Password,URL,Notes,Tags\nTest,user,pass,http://example.com,notes,tag1";
        let rows = parse_csv_data(csv_data).unwrap();

        assert_eq!(rows.len(), 1);
        assert_eq!(rows[0].title, "Test");
        assert_eq!(rows[0].username, "user");
        assert_eq!(rows[0].password, "pass");
        assert_eq!(rows[0].url, Some("http://example.com".to_string()));
        assert_eq!(rows[0].notes, Some("notes".to_string()));
        assert_eq!(rows[0].tags, Some("tag1".to_string()));
    }

    #[test]
    fn test_csv_generation() {
        let entry = PasswordEntry {
            id: Uuid::new_v4(),
            title: "Test Entry".to_string(),
            username: "testuser".to_string(),
            password: Some("testpass".to_string()),
            url: Some("https://example.com".to_string()),
            notes: Some("Test notes".to_string()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            tags: vec!["work".to_string(), "important".to_string()],
            category: "General".to_string(),
            favorite: false,
        };

        let csv_data = generate_csv_data(&[entry]).unwrap();
        assert!(csv_data.contains("Test Entry"));
        assert!(csv_data.contains("testuser"));
        assert!(csv_data.contains("testpass"));
        assert!(csv_data.contains("https://example.com"));
        assert!(csv_data.contains("work, important"));
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("normal_file.txt"), "normal_file.txt");
        assert_eq!(
            sanitize_filename("file with spaces.txt"),
            "file_with_spaces.txt"
        );
        assert_eq!(
            sanitize_filename("file/with\\bad:chars.txt"),
            "file_with_bad_chars.txt"
        );
        assert_eq!(sanitize_filename("file<>|?*.txt"), "file_____.txt");
    }
}
