use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;

// Default category for entries
fn default_category() -> String {
    "General".to_string()
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Category {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordEntry {
    pub id: Uuid,
    pub title: String,
    pub username: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub password: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub notes: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub tags: Vec<String>,
    #[serde(default = "default_category")]
    pub category: String,
    #[serde(default)]
    pub favorite: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileEntry {
    pub id: Uuid,
    pub filename: String,
    pub original_path: String,
    pub description: Option<String>,
    pub file_size: u64,
    pub mime_type: String,
    pub created_at: DateTime<Utc>,
    pub tags: Vec<String>,
    #[serde(default = "default_category")]
    pub category: String,
    #[serde(default)]
    pub favorite: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VaultMetadata {
    pub version: String,
    pub created_at: DateTime<Utc>,
    pub last_modified: DateTime<Utc>,
    pub entry_count: usize,
    pub file_count: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vault {
    pub metadata: VaultMetadata,
    pub passwords: Vec<PasswordEntry>,
    pub files: Vec<FileEntry>,
    #[serde(default)]
    pub categories: Vec<Category>,
}

impl Default for Vault {
    fn default() -> Self {
        let mut vault = Self {
            metadata: VaultMetadata {
                version: env!("CARGO_PKG_VERSION").to_string(),
                created_at: Utc::now(),
                last_modified: Utc::now(),
                entry_count: 0,
                file_count: 0,
            },
            passwords: Vec::new(),
            files: Vec::new(),
            categories: Vec::new(),
        };

        // Add default categories
        vault.add_default_categories();
        vault
    }
}

impl Vault {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_password(&mut self, entry: PasswordEntry) {
        self.passwords.push(entry);
        self.metadata.entry_count = self.passwords.len();
        self.metadata.last_modified = Utc::now();
    }

    pub fn add_file(&mut self, entry: FileEntry) {
        self.files.push(entry);
        self.metadata.file_count = self.files.len();
        self.metadata.last_modified = Utc::now();
    }

    pub fn search_passwords(&self, query: &str) -> Vec<&PasswordEntry> {
        let query = query.to_lowercase();
        self.passwords
            .iter()
            .filter(|entry| {
                entry.title.to_lowercase().contains(&query)
                    || entry.username.to_lowercase().contains(&query)
                    || entry
                        .url
                        .as_ref()
                        .map(|u| u.to_lowercase().contains(&query))
                        .unwrap_or(false)
                    || entry
                        .notes
                        .as_ref()
                        .map(|n| n.to_lowercase().contains(&query))
                        .unwrap_or(false)
                    || entry
                        .tags
                        .iter()
                        .any(|tag| tag.to_lowercase().contains(&query))
            })
            .collect()
    }

    pub fn search_files(&self, query: &str) -> Vec<&FileEntry> {
        let query = query.to_lowercase();
        self.files
            .iter()
            .filter(|entry| {
                entry.filename.to_lowercase().contains(&query)
                    || entry
                        .description
                        .as_ref()
                        .map(|d| d.to_lowercase().contains(&query))
                        .unwrap_or(false)
                    || entry
                        .tags
                        .iter()
                        .any(|tag| tag.to_lowercase().contains(&query))
            })
            .collect()
    }

    // Category management methods
    pub fn add_default_categories(&mut self) {
        let default_categories = vec![
            ("General", "General purpose entries", "🔧"),
            ("Work", "Work-related accounts", "💼"),
            ("Personal", "Personal accounts", "👤"),
            ("Social", "Social media accounts", "📱"),
            ("Finance", "Banking and financial", "💰"),
            ("Development", "Development tools", "⚙️"),
            ("Shopping", "E-commerce accounts", "🛒"),
            ("Entertainment", "Streaming and games", "🎮"),
        ];

        for (name, desc, icon) in default_categories {
            if !self.categories.iter().any(|c| c.name == name) {
                self.categories.push(Category {
                    id: Uuid::new_v4(),
                    name: name.to_string(),
                    description: Some(desc.to_string()),
                    color: None,
                    icon: Some(icon.to_string()),
                    created_at: Utc::now(),
                });
            }
        }
    }

    pub fn add_category(&mut self, name: String, description: Option<String>, icon: Option<String>) -> Result<Uuid, String> {
        if self.categories.iter().any(|c| c.name == name) {
            return Err("Category already exists".to_string());
        }

        let category = Category {
            id: Uuid::new_v4(),
            name,
            description,
            color: None,
            icon,
            created_at: Utc::now(),
        };

        let id = category.id;
        self.categories.push(category);
        self.metadata.last_modified = Utc::now();
        Ok(id)
    }

    pub fn get_categories(&self) -> &Vec<Category> {
        &self.categories
    }

    pub fn get_category_by_name(&self, name: &str) -> Option<&Category> {
        self.categories.iter().find(|c| c.name == name)
    }



    pub fn get_category_stats(&self) -> HashMap<String, (usize, usize)> {
        let mut stats = HashMap::new();

        // Initialize with all categories
        for category in &self.categories {
            stats.insert(category.name.clone(), (0, 0));
        }

        // Count passwords by category
        for password in &self.passwords {
            let entry = stats.entry(password.category.clone()).or_insert((0, 0));
            entry.0 += 1;
        }

        // Count files by category
        for file in &self.files {
            let entry = stats.entry(file.category.clone()).or_insert((0, 0));
            entry.1 += 1;
        }

        stats
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_password_entry() -> PasswordEntry {
        PasswordEntry {
            id: Uuid::new_v4(),
            title: "Test Entry".to_string(),
            username: "testuser".to_string(),
            password: Some("testpass".to_string()),
            url: Some("https://example.com".to_string()),
            notes: Some("Test notes".to_string()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            tags: vec!["work".to_string(), "important".to_string()],
            category: "Work".to_string(),
            favorite: false,
        }
    }

    fn create_test_file_entry() -> FileEntry {
        FileEntry {
            id: Uuid::new_v4(),
            filename: "test_file.txt".to_string(),
            original_path: "/path/to/test_file.txt".to_string(),
            description: Some("Test file description".to_string()),
            file_size: 1024,
            mime_type: "text/plain".to_string(),
            created_at: Utc::now(),
            tags: vec!["documents".to_string(), "test".to_string()],
            category: "General".to_string(),
            favorite: false,
        }
    }

    #[test]
    fn test_vault_creation() {
        let vault = Vault::new();

        assert_eq!(vault.passwords.len(), 0);
        assert_eq!(vault.files.len(), 0);
        assert_eq!(vault.metadata.entry_count, 0);
        assert_eq!(vault.metadata.file_count, 0);
        assert_eq!(vault.metadata.version, env!("CARGO_PKG_VERSION"));
    }

    #[test]
    fn test_add_password() {
        let mut vault = Vault::new();
        let entry = create_test_password_entry();
        let entry_id = entry.id;

        vault.add_password(entry);

        assert_eq!(vault.passwords.len(), 1);
        assert_eq!(vault.metadata.entry_count, 1);
        assert_eq!(vault.passwords[0].id, entry_id);
        assert_eq!(vault.passwords[0].title, "Test Entry");
    }

    #[test]
    fn test_add_file() {
        let mut vault = Vault::new();
        let entry = create_test_file_entry();
        let entry_id = entry.id;

        vault.add_file(entry);

        assert_eq!(vault.files.len(), 1);
        assert_eq!(vault.metadata.file_count, 1);
        assert_eq!(vault.files[0].id, entry_id);
        assert_eq!(vault.files[0].filename, "test_file.txt");
    }

    #[test]
    fn test_search_passwords_by_title() {
        let mut vault = Vault::new();
        let mut entry1 = create_test_password_entry();
        entry1.title = "GitHub Account".to_string();
        let mut entry2 = create_test_password_entry();
        entry2.title = "Gmail Account".to_string();

        vault.add_password(entry1);
        vault.add_password(entry2);

        let results = vault.search_passwords("github");
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].title, "GitHub Account");

        let results = vault.search_passwords("account");
        assert_eq!(results.len(), 2);
    }

    #[test]
    fn test_search_passwords_by_username() {
        let mut vault = Vault::new();
        let mut entry = create_test_password_entry();
        entry.username = "<EMAIL>".to_string();

        vault.add_password(entry);

        let results = vault.search_passwords("john.doe");
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].username, "<EMAIL>");
    }

    #[test]
    fn test_search_passwords_by_tags() {
        let mut vault = Vault::new();
        let mut entry = create_test_password_entry();
        entry.tags = vec!["work".to_string(), "social".to_string()];

        vault.add_password(entry);

        let results = vault.search_passwords("work");
        assert_eq!(results.len(), 1);

        let results = vault.search_passwords("personal");
        assert_eq!(results.len(), 0);
    }

    #[test]
    fn test_search_files_by_filename() {
        let mut vault = Vault::new();
        let mut entry1 = create_test_file_entry();
        entry1.filename = "document.pdf".to_string();
        entry1.description = Some("Important document".to_string());
        entry1.tags = vec!["work".to_string(), "pdf".to_string()];

        let mut entry2 = create_test_file_entry();
        entry2.filename = "image.jpg".to_string();
        entry2.description = Some("Profile picture".to_string());
        entry2.tags = vec!["personal".to_string(), "photo".to_string()];

        vault.add_file(entry1);
        vault.add_file(entry2);

        let results = vault.search_files("document");
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].filename, "document.pdf");

        let results = vault.search_files("image");
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].filename, "image.jpg");

        // Test searching by description
        let results = vault.search_files("important");
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].filename, "document.pdf");
    }

    #[test]
    fn test_search_case_insensitive() {
        let mut vault = Vault::new();
        let mut entry = create_test_password_entry();
        entry.title = "GitHub Account".to_string();

        vault.add_password(entry);

        let results = vault.search_passwords("GITHUB");
        assert_eq!(results.len(), 1);

        let results = vault.search_passwords("github");
        assert_eq!(results.len(), 1);

        let results = vault.search_passwords("GitHub");
        assert_eq!(results.len(), 1);
    }

    #[test]
    fn test_metadata_updates() {
        let mut vault = Vault::new();
        let initial_time = vault.metadata.last_modified;

        // Small delay to ensure time difference
        std::thread::sleep(std::time::Duration::from_millis(1));

        vault.add_password(create_test_password_entry());
        assert!(vault.metadata.last_modified > initial_time);
        assert_eq!(vault.metadata.entry_count, 1);

        let password_time = vault.metadata.last_modified;
        std::thread::sleep(std::time::Duration::from_millis(1));

        vault.add_file(create_test_file_entry());
        assert!(vault.metadata.last_modified > password_time);
        assert_eq!(vault.metadata.file_count, 1);
    }
}
