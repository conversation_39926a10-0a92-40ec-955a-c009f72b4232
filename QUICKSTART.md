# 🚀 CipherVault Quick Start Guide

Get up and running with Cip<PERSON><PERSON><PERSON> in 5 minutes!

## Prerequisites
- Rust 1.70+ installed
- Terminal/Command Prompt

## Step 1: Build the Application
```bash
cargo build --release
```

## Step 2: Initialize Your Vault
```bash
./target/release/ciphervault init
```
- Enter a strong master password when prompted
- This creates an encrypted vault file

## Step 3: Add Your First Password
```bash
./target/release/ciphervault add-password --title "GitH<PERSON>" --username "your-username"
```
- The password will be prompted interactively
- You can also generate a strong password automatically

## Step 4: Add a File (Optional)
```bash
./target/release/ciphervault add-file --file-path /path/to/your/secret-file.pdf
```

## Step 5: List Your Entries
```bash
./target/release/ciphervault list
```

## Common Commands

### Search for Entries
```bash
./target/release/ciphervault search "github"
```

### Import from CSV
```bash
./target/release/ciphervault import --input passwords.csv
```

### Export to CSV
```bash
./target/release/ciphervault export --output my-passwords.csv
```

### Change Master Password
```bash
./target/release/ciphervault change-password
```

## Security Tips
1. **Use a strong master password** - This is your only defense
2. **Keep your vault file secure** - Don't share it
3. **Make regular backups** - Store encrypted backups safely
4. **Use different passwords** - Generate unique passwords for each service

## File Types Supported
- **Images**: JPG, PNG, GIF, BMP, WebP, SVG
- **Videos**: MP4, AVI, MOV, WMV, FLV, WebM, MKV
- **Documents**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Text**: TXT, CSV, JSON, XML, HTML, CSS, JS
- **Archives**: ZIP, RAR, 7Z, TAR, GZ
- **Audio**: MP3, WAV, FLAC, AAC, OGG
- **Any other file type**

## Need Help?
- Run `./target/release/ciphervault --help` for all commands
- Run `./target/release/ciphervault <command> --help` for specific help
- Check the full README.md for detailed documentation

## Example Workflow
```bash
# 1. Initialize vault
./target/release/ciphervault init

# 2. Add passwords
./target/release/ciphervault add-password --title "Email" --username "<EMAIL>"
./target/release/ciphervault add-password --title "Bank" --username "account123"

# 3. Add important files
./target/release/ciphervault add-file --file-path ~/Documents/private-key.pem
./target/release/ciphervault add-file --file-path ~/Pictures/id-card.jpg

# 4. Search and manage
./target/release/ciphervault search "bank"
./target/release/ciphervault list

# 5. Export for backup
./target/release/ciphervault export --output backup-passwords.csv
```

**🔐 Your secrets are now secure with military-grade encryption!** 